import OpenAI from "openai";

/**
 * GPT Handler Class
 * Encapsulates all OpenAI GPT API interactions, analysis, and trading signal generation
 * Follows clean separation of concerns pattern for trading bot architecture
 */
class GPTHandler {
  constructor(apiKey, symbol, interval) {
    this.symbol = symbol;
    this.interval = interval;

    // Initialize OpenAI client
    this.openai = new OpenAI({ apiKey });

    // Model configurations
    this.models = {
      vision: "gpt-4o",
      text: "gpt-4o-mini",
      analysis: "gpt-4o-mini",
    };

    // Default parameters
    this.defaultParams = {
      temperature: 0.1,
      maxTokens: {
        vision: 2000,
        text: 800,
        analysis: 800,
      },
    };
  }

  /**
   * Analyze trading data with GPT for multi-timeframe analysis
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @param {Object} tradingSignal - Trading signal data
   * @returns {Promise<string>} GPT analysis result
   */
  async analyzeWithGPT(multiTimeframeAnalysis, tradingSignal) {
    const tf4h = multiTimeframeAnalysis["4h"];
    const tf1h = multiTimeframeAnalysis["1h"];
    const tf15m = multiTimeframeAnalysis["15m"];

    const currentPrice = tf15m.candles.at(-1).close;
    const currentRsi = tf15m.indicators.rsi.at(-1);
    const currentEma20 = tf15m.indicators.ema20.at(-1);
    const currentEma50 = tf15m.indicators.ema50.at(-1);

    const text = `You are a professional trading analyst. Analyze ${
      this.symbol
    } ${this.interval} with multi-timeframe data for educational purposes:

CURRENT MARKET DATA:
- Current Price: $${currentPrice}
- 15M Trend: ${tf15m.trend.trend} (Confidence: ${tf15m.trend.confidence.toFixed(
      2
    )})
- 1H Trend: ${tf1h.trend.trend} (Confidence: ${tf1h.trend.confidence.toFixed(
      2
    )})
- 4H Trend: ${tf4h.trend.trend} (Confidence: ${tf4h.trend.confidence.toFixed(
      2
    )})

TECHNICAL INDICATORS (15M):
- RSI(14): ${currentRsi?.toFixed(1)}
- EMA20: $${currentEma20?.toFixed(2)}
- EMA50: $${currentEma50?.toFixed(2)}

TRADING SIGNAL ANALYSIS:
- Major Trend: ${tradingSignal.majorTrend}
- Entry Signal: ${tradingSignal.entrySignal}
- Confidence: ${tradingSignal.confidence.toFixed(2)}
- Recommendation: ${tradingSignal.recommendation}

RISK LEVELS:
- Support: $${tradingSignal.riskLevels.support?.toFixed(2) || "N/A"}
- Resistance: $${tradingSignal.riskLevels.resistance?.toFixed(2) || "N/A"}
- Stop Loss: $${tradingSignal.riskLevels.stopLoss?.toFixed(2) || "N/A"}

REQUIRED FORMAT (HTML only, no markdown):

<b>📊 MULTI-TIMEFRAME ANALYSIS:</b>
• <b>4H:</b> [Trend analysis]
• <b>1H:</b> [Trend analysis] 
• <b>15M:</b> [Trend analysis]

<b>🎯 TRADING SCENARIO:</b>
• <b>Type:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Specific entry conditions]
• <b>Confirmation:</b> [Signals to wait for]
• <b>Entry/SL/TP:</b> Entry [$price], SL [$price], TP [$price] (RR 1:[ratio])
• <b>Invalidation:</b> [Conditions that cancel setup]

<b>💡 Confidence:</b> [X]%

IMPORTANT: Use only <b></b> HTML tags, no markdown. Be specific with prices. Max 500 characters.`;

    const res = await this.openai.chat.completions.create({
      model: this.models.text,
      messages: [
        {
          role: "system",
          content:
            "You are a professional trading analyst. MUST respond in EXACT HTML format requested. Use only <b></b> tags, no markdown. Be concise and professional.",
        },
        { role: "user", content: text },
      ],
    });

    return res.choices[0].message.content;
  }

  /**
   * Legacy GPT analysis for backward compatibility
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @returns {Promise<string>} Legacy GPT analysis result
   */
  async legacyAnalyzeWithGPT(candles, indicators) {
    const last = candles.at(-1);

    const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${this.symbol} khung ${
      this.interval
    } với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[
      indicators.ema20.at(-1),
      indicators.ema50.at(-1),
      indicators.ema89.at(-1),
    ]
      .map((v) => v?.toFixed(2))
      .join("/")}
- RSI(14): ${indicators.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${indicators.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]
<b>🎯 TÍN HIỆU:</b> [Mua/Bán/Chờ] - [Điều kiện entry]
<b>💰 MỤC TIÊU:</b> Entry [giá], SL [giá], TP [giá]
<b>⚠️ RỦI RO:</b> [Mức độ rủi ro và cảnh báo]
<b>📊 RSI:</b> [Phân tích RSI hiện tại]

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

    const res = await this.openai.chat.completions.create({
      model: this.models.text,
      messages: [
        {
          role: "system",
          content:
            "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp.",
        },
        { role: "user", content: text },
      ],
    });

    return res.choices[0].message.content;
  }

  /**
   * Repair common JSON formatting issues
   * @param {string} jsonText - JSON text to repair
   * @returns {string} Repaired JSON text
   */
  repairCommonJsonIssues(jsonText) {
    let repaired = jsonText;

    // Remove any trailing commas before closing braces/brackets
    repaired = repaired.replace(/,(\s*[}\]])/g, "$1");

    // Fix unescaped quotes in string values
    repaired = repaired.replace(
      /"([^"]*)"([^"]*)"([^"]*)":/g,
      '"$1\\"$2\\"$3":'
    );

    // Remove any control characters that might break JSON
    repaired = repaired.replace(/[\x00-\x1F\x7F]/g, "");

    // Ensure proper quote escaping
    repaired = repaired.replace(/\\"/g, '\\"');

    return repaired;
  }

  /**
   * Create fallback analysis when AI analysis fails
   * @param {string} timeframe - Timeframe identifier
   * @returns {Object} Fallback analysis object
   */
  createFallbackAnalysis(timeframe) {
    return {
      trend: {
        direction: "SIDEWAYS",
        strength: 5,
        confidence: 3,
        reasoning: `Fallback analysis for ${timeframe} - AI vision unavailable`,
      },
      patterns: {
        chart_patterns: [],
        candlestick_patterns: [],
        pattern_significance: "LOW",
      },
      levels: {
        support: [],
        resistance: [],
        key_level_proximity: "Unknown",
      },
      moving_averages: {
        price_vs_ema20: "AT",
        price_vs_ema50: "AT",
        price_vs_ema200: "AT",
        ema_alignment: "MIXED",
        ema_analysis: "Fallback analysis - limited data",
      },
      momentum: {
        direction: "NEUTRAL",
        strength: 5,
        momentum_analysis: "Fallback analysis - limited data",
      },
      trading_signal: {
        signal: "HOLD",
        entry_zone: "Market price",
        stop_loss: "Use 2% rule",
        take_profit: "Use 2:1 RR",
        risk_reward: "1:2",
        signal_reasoning: "Fallback analysis - manual review required",
      },
      overall_assessment: {
        market_condition: "RANGING",
        trade_quality: "LOW",
        confidence_score: 3,
        key_insights: `Fallback analysis for ${timeframe} - AI vision failed`,
      },
    };
  }

  /**
   * Create analysis from text when JSON parsing fails
   * @param {string} responseText - AI response text
   * @param {string} timeframe - Timeframe identifier
   * @returns {Object} Analysis object extracted from text
   */
  createAnalysisFromText(responseText, timeframe) {
    console.log(
      `🔄 Attempting to extract analysis from text for ${timeframe}...`
    );
    console.log(`Text response preview: ${responseText.substring(0, 150)}...`);

    // Check if response contains apology or error message
    const text = responseText.toLowerCase();
    const isApologyResponse =
      text.includes("i'm sorry") ||
      text.includes("i cannot") ||
      text.includes("unable to") ||
      text.includes("apologize");

    if (isApologyResponse) {
      console.log(
        `⚠️ AI provided apology response for ${timeframe}, using neutral fallback`
      );
      return this.createFallbackAnalysis(timeframe);
    }

    // Try to extract trend direction from text
    let trendDirection = "SIDEWAYS";
    let signal = "HOLD";
    let confidence = 5;

    if (
      text.includes("bullish") ||
      text.includes("uptrend") ||
      text.includes("buy")
    ) {
      trendDirection = "BULLISH";
      signal = "BUY";
      confidence = 6;
    } else if (
      text.includes("bearish") ||
      text.includes("downtrend") ||
      text.includes("sell")
    ) {
      trendDirection = "BEARISH";
      signal = "SELL";
      confidence = 6;
    }

    // Extract confidence if mentioned
    const confidenceMatch = text.match(/confidence[:\s]*(\d+)/i);
    if (confidenceMatch) {
      confidence = Math.min(10, Math.max(1, parseInt(confidenceMatch[1])));
    }

    const trendStrength = confidence;

    return {
      trend: {
        direction: trendDirection,
        strength: trendStrength,
        confidence: confidence,
        reasoning: `Extracted from text analysis for ${timeframe}`,
      },
      patterns: {
        chart_patterns: [],
        candlestick_patterns: [],
        pattern_significance: "LOW",
      },
      levels: {
        support: [],
        resistance: [],
        key_level_proximity: "Unknown",
      },
      moving_averages: {
        price_vs_ema20: "AT",
        price_vs_ema50: "AT",
        price_vs_ema200: "AT",
        ema_alignment: "MIXED",
        ema_analysis: "Text analysis - limited data",
      },
      momentum: {
        direction: "NEUTRAL",
        strength: 5,
        momentum_analysis: "Text analysis - limited data",
      },
      trading_signal: {
        signal: signal,
        entry_zone: "Market price",
        stop_loss: "Use 2% rule",
        take_profit: "Use 2:1 RR",
        risk_reward: "1:2",
        signal_reasoning: "Extracted from text analysis",
      },
      overall_assessment: {
        market_condition: "RANGING",
        trade_quality: "LOW",
        confidence_score: confidence,
        key_insights: `Text-based analysis for ${timeframe} - JSON parsing failed`,
      },
    };
  }

  /**
   * Analyze chart with AI vision
   * @param {Buffer} chartBuffer - Chart image buffer
   * @param {string} timeframe - Timeframe identifier
   * @param {number} currentPrice - Current price
   * @returns {Promise<Object>} AI vision analysis result
   */
  async analyzeChartWithAIVision(chartBuffer, timeframe, currentPrice) {
    try {
      console.log(`🔍 Analyzing ${timeframe} chart with AI vision...`);

      // Convert buffer to base64 for OpenAI Vision API
      const base64Image = chartBuffer.toString("base64");

      const prompt = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy phân tích biểu đồ giá ${timeframe} của ${this.symbol} cho mục đích học tập và nghiên cứu.

BỐI CẢNH PHÂN TÍCH:
- Mã tài sản: ${this.symbol}
- Khung thời gian: ${timeframe}
- Giá hiện tại: $${currentPrice}
- Mục đích: Giáo dục và nghiên cứu

YÊU CẦU PHÂN TÍCH:
1. Phân tích xu hướng tổng thể (BULLISH/BEARISH/SIDEWAYS)
2. Nhận diện các mô hình biểu đồ quan trọng
3. Phân tích vị trí giá so với các đường EMA
4. Đánh giá momentum và RSI
5. Xác định các mức hỗ trợ/kháng cự
6. Đưa ra tín hiệu giao dịch giáo dục

ĐỊNH DẠNG PHẢN HỒI - CHỈ JSON HỢP LỆ:
{
  "trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Lý do phân tích xu hướng"
  },
  "patterns": {
    "chart_patterns": ["Danh sách các mô hình biểu đồ"],
    "candlestick_patterns": ["Các mô hình nến"],
    "pattern_significance": "HIGH|MEDIUM|LOW"
  },
  "levels": {
    "support": [giá hỗ trợ],
    "resistance": [giá kháng cự],
    "key_level_proximity": "NEAR_SUPPORT|NEAR_RESISTANCE|BETWEEN_LEVELS"
  },
  "moving_averages": {
    "price_vs_ema20": "ABOVE|BELOW|AT",
    "price_vs_ema50": "ABOVE|BELOW|AT",
    "price_vs_ema200": "ABOVE|BELOW|AT",
    "ema_alignment": "BULLISH|BEARISH|MIXED",
    "ema_analysis": "Phân tích chi tiết về EMA"
  },
  "momentum": {
    "direction": "INCREASING|DECREASING|NEUTRAL",
    "strength": 1-10,
    "momentum_analysis": "Phân tích momentum indicators"
  },
  "trading_signal": {
    "signal": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL",
    "entry_zone": "Vùng giá entry đề xuất",
    "stop_loss": "Mức stop loss đề xuất",
    "take_profit": "Mức take profit đề xuất",
    "risk_reward": "Tỷ lệ risk/reward",
    "signal_reasoning": "Lý do tín hiệu này"
  },
  "overall_assessment": {
    "market_condition": "TRENDING|RANGING|VOLATILE|CONSOLIDATING",
    "trade_quality": "HIGH|MEDIUM|LOW",
    "confidence_score": 1-10,
    "key_insights": "Những nhận định quan trọng nhất"
  }
}

LƯU Ý QUAN TRỌNG:
- CHỈ trả lời bằng JSON hợp lệ, không có text giải thích thêm
- Phân tích mang tính giáo dục, không phải lời khuyên đầu tư
- Dựa trên những gì nhìn thấy trong biểu đồ
- Confidence score phản ánh độ tin cậy của phân tích`;

      const response = await this.openai.chat.completions.create({
        model: this.models.vision,
        messages: [
          {
            role: "system",
            content:
              "You are a technical analysis expert. You MUST respond ONLY with valid JSON. Do not include any explanatory text, apologies, or markdown formatting. Start your response with { and end with }. If you cannot analyze the chart, still respond with valid JSON using placeholder values.",
          },
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/png;base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: this.defaultParams.maxTokens.vision,
        temperature: this.defaultParams.temperature,
      });

      const analysisText = response.choices[0].message.content;

      // Clean and parse JSON response
      let analysis;
      try {
        // Try to extract JSON from the response
        let jsonText = analysisText.trim();

        console.log(
          `🔍 Raw AI response for ${timeframe}: ${analysisText.substring(
            0,
            100
          )}...`
        );

        // Look for JSON block if wrapped in markdown
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim();
          console.log(`📝 Found JSON in markdown block for ${timeframe}`);
        }

        // Extract JSON from response if it contains other text
        const jsonStart = jsonText.indexOf("{");
        const jsonEnd = jsonText.lastIndexOf("}");
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
          console.log(`🔧 Extracted JSON substring for ${timeframe}`);
        }

        console.log(`🔍 Attempting to parse cleaned JSON for ${timeframe}...`);

        // Try to repair common JSON issues before parsing
        jsonText = this.repairCommonJsonIssues(jsonText);

        analysis = JSON.parse(jsonText);

        // Validate required fields
        if (
          !analysis.trend ||
          !analysis.trading_signal ||
          !analysis.overall_assessment
        ) {
          throw new Error("Missing required fields in AI analysis");
        }

        console.log(`✅ Successfully parsed JSON for ${timeframe}`);
      } catch (parseError) {
        console.error(
          `❌ Failed to parse AI analysis for ${timeframe}:`,
          parseError.message
        );
        console.error(
          `Raw response (first 300 chars): ${analysisText.substring(0, 300)}...`
        );

        // Log the cleaned JSON attempt for debugging
        if (parseError.message.includes("JSON")) {
          const jsonStart = analysisText.indexOf("{");
          const jsonEnd = analysisText.lastIndexOf("}");
          if (jsonStart !== -1 && jsonEnd !== -1) {
            const attemptedJson = analysisText.substring(
              jsonStart,
              jsonEnd + 1
            );
            console.error(
              `Attempted JSON parse: ${attemptedJson.substring(0, 200)}...`
            );
          }
        }

        // Try to create analysis from text if JSON parsing fails
        analysis = this.createAnalysisFromText(analysisText, timeframe);
      }

      console.log(`✅ AI analysis completed for ${timeframe}`);
      return analysis;
    } catch (error) {
      console.error(`❌ AI vision analysis failed for ${timeframe}:`, error);
      return this.createFallbackAnalysis(timeframe);
    }
  }

  /**
   * Perform AI visual analysis on multi-timeframe charts
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @returns {Promise<Object>} AI visual analysis results
   */
  async performAIVisualAnalysis(multiTimeframeAnalysis) {
    try {
      console.log("🤖 Starting AI-powered visual chart analysis...");

      // Generate clean charts for AI analysis
      const charts = await this.chartHandler.generateMultiTimeframeChartsForAI(
        multiTimeframeAnalysis
      );

      // Analyze each timeframe with AI vision
      const aiAnalysis = {};
      const analysisPromises = [];

      for (const [timeframe, chartData] of Object.entries(charts)) {
        const currentPrice =
          multiTimeframeAnalysis[timeframe].candles.at(-1).close;

        const promise = this.analyzeChartWithAIVision(
          chartData.buffer,
          timeframe,
          currentPrice
        )
          .then((analysis) => {
            aiAnalysis[timeframe] = analysis;
            console.log(`✅ Successfully analyzed ${timeframe} chart`);
          })
          .catch((error) => {
            console.error(
              `❌ Failed to analyze ${timeframe} chart:`,
              error.message
            );
            aiAnalysis[timeframe] = this.createFallbackAnalysis(timeframe);
          });

        analysisPromises.push(promise);
      }

      // Wait for all analysis to complete
      await Promise.all(analysisPromises);

      console.log("✅ AI visual analysis completed for all timeframes");
      return aiAnalysis;
    } catch (error) {
      console.error("❌ AI visual analysis failed:", error);
      throw error;
    }
  }

  /**
   * Create fallback trading signal when AI generation fails
   * @returns {Object} Fallback trading signal
   */
  createFallbackTradingSignal() {
    return {
      major_trend: {
        direction: "SIDEWAYS",
        timeframe_alignment: "MIXED",
        strength: 5,
        reasoning: "Fallback analysis - AI signal generation failed",
      },
      trading_recommendation: {
        action: "HOLD",
        confidence: 1,
        entry_price: "Market price",
        stop_loss: "Use 2% rule",
        take_profit_1: "Use 2:1 RR",
        take_profit_2: "Use 3:1 RR",
        risk_reward_ratio: "1:2",
        position_size: "SMALL",
        reasoning: "AI signal generation failed - manual review required",
      },
      risk_assessment: {
        risk_level: "HIGH",
        key_risks: ["AI analysis unavailable", "Manual review required"],
        invalidation_level: "N/A",
        market_conditions: "Unknown - AI analysis failed",
      },
      execution_plan: {
        entry_strategy: "Wait for manual analysis",
        exit_strategy: "N/A",
        monitoring_points: [],
        time_horizon: "N/A",
      },
      overall_assessment: {
        trade_quality: "POOR",
        market_opportunity: 1,
        final_confidence: 1,
        key_message: "AI vision analysis failed - manual review required",
      },
      metadata: {
        analysis_type: "FALLBACK",
        timestamp: new Date().toISOString(),
        symbol: this.symbol,
        current_price: 0,
        timeframes_analyzed: [],
      },
    };
  }

  /**
   * Generate AI-powered trading signal
   * @param {Object} aiAnalysis - AI analysis results
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @returns {Promise<Object>} AI trading signal
   */
  async generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
    try {
      console.log("🎯 Generating AI-powered trading signal...");

      const { "4h": ai4h, "1h": ai1h, "15m": ai15m } = aiAnalysis;
      const currentPrice = multiTimeframeAnalysis["15m"].candles.at(-1).close;

      // Create comprehensive multi-timeframe prompt for educational purposes
      const multiTimeframePrompt = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy tạo một phân tích giáo dục dựa trên dữ liệu đa khung thời gian cho mục đích học tập và nghiên cứu.

THÔNG TIN THỊ TRƯỜNG:
- Tài sản: ${this.symbol}
- Giá hiện tại: $${currentPrice}
- Mục đích: Phân tích giáo dục và nghiên cứu

PHÂN TÍCH ĐA KHUNG THỜI GIAN:

KHUNG 4H:
- Xu hướng: ${ai4h.trend.direction} (Độ mạnh: ${ai4h.trend.strength}/10)
- Tín hiệu: ${ai4h.trading_signal.signal}
- Điều kiện thị trường: ${ai4h.overall_assessment.market_condition}
- Độ tin cậy: ${ai4h.overall_assessment.confidence_score}/10

KHUNG 1H:
- Xu hướng: ${ai1h.trend.direction} (Độ mạnh: ${ai1h.trend.strength}/10)
- Tín hiệu: ${ai1h.trading_signal.signal}
- Điều kiện thị trường: ${ai1h.overall_assessment.market_condition}
- Độ tin cậy: ${ai1h.overall_assessment.confidence_score}/10

KHUNG 15M:
- Xu hướng: ${ai15m.trend.direction} (Độ mạnh: ${ai15m.trend.strength}/10)
- Tín hiệu: ${ai15m.trading_signal.signal}
- Điều kiện thị trường: ${ai15m.overall_assessment.market_condition}
- Độ tin cậy: ${ai15m.overall_assessment.confidence_score}/10

YÊU CẦU PHÂN TÍCH TỔNG HỢP:
1. Đánh giá xu hướng chính dựa trên đa khung thời gian
2. Xác định mức độ đồng thuận giữa các khung thời gian
3. Đưa ra khuyến nghị giao dịch giáo dục thận trọng
4. Đánh giá rủi ro và cơ hội
5. Lập kế hoạch thực hiện chi tiết

ĐỊNH DẠNG PHẢN HỒI - CHỈ JSON HỢP LỆ:
{
  "major_trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "timeframe_alignment": "STRONG|MODERATE|WEAK|CONFLICTING",
    "strength": 1-10,
    "reasoning": "Lý do phân tích xu hướng chính"
  },
  "trading_recommendation": {
    "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL|NO_TRADE",
    "confidence": 1-10,
    "entry_price": "Giá entry cụ thể hoặc vùng giá",
    "stop_loss": "Mức stop loss cụ thể",
    "take_profit_1": "Mục tiêu take profit đầu tiên",
    "take_profit_2": "Mục tiêu take profit thứ hai",
    "risk_reward_ratio": "Tỷ lệ risk/reward",
    "position_size": "SMALL|MEDIUM|LARGE",
    "reasoning": "Lý do chi tiết cho khuyến nghị này"
  },
  "risk_assessment": {
    "risk_level": "LOW|MEDIUM|HIGH",
    "key_risks": ["Danh sách các rủi ro chính"],
    "invalidation_level": "Mức giá làm mất hiệu lực setup",
    "market_conditions": "Đánh giá điều kiện thị trường hiện tại"
  },
  "execution_plan": {
    "entry_strategy": "Cách thức vào lệnh",
    "exit_strategy": "Cách thức quản lý vị thế",
    "monitoring_points": ["Các mức giá cần theo dõi"],
    "time_horizon": "Thời gian dự kiến của giao dịch"
  },
  "overall_assessment": {
    "trade_quality": "EXCELLENT|GOOD|FAIR|POOR",
    "market_opportunity": 1-10,
    "final_confidence": 1-10,
    "key_message": "Thông điệp quan trọng nhất cho trader"
  }
}

QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Xem xét tất cả khung thời gian nhưng ưu tiên xu hướng khung thời gian cao hơn cho hướng chính.`;

      const response = await this.openai.chat.completions.create({
        model: this.models.vision,
        messages: [
          {
            role: "system",
            content:
              "Bạn là chuyên gia phân tích kỹ thuật giáo dục với hơn 20 năm kinh nghiệm. Tạo phân tích giáo dục thận trọng, có lý lẽ dựa trên đa khung thời gian cho mục đích học tập. Luôn nhấn mạnh quản lý rủi ro và tính chất giáo dục của phân tích.",
          },
          {
            role: "user",
            content: multiTimeframePrompt,
          },
        ],
        max_tokens: this.defaultParams.maxTokens.vision,
        temperature: this.defaultParams.temperature,
      });

      const signalText = response.choices[0].message.content;

      // Parse trading signal JSON
      let tradingSignal;
      try {
        // Extract JSON from response
        let jsonText = signalText.trim();

        // Look for JSON block if wrapped in markdown
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim();
        }

        // Extract JSON from response if it contains other text
        const jsonStart = jsonText.indexOf("{");
        const jsonEnd = jsonText.lastIndexOf("}");
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
        }

        console.log("🔍 Attempting to parse AI trading signal...");
        tradingSignal = JSON.parse(jsonText);

        // Validate required fields
        if (
          !tradingSignal.major_trend ||
          !tradingSignal.trading_recommendation
        ) {
          throw new Error("Missing required fields in trading signal");
        }
      } catch (parseError) {
        console.error("❌ Failed to parse AI trading signal:", parseError);
        console.error(`Raw response: ${signalText.substring(0, 200)}...`);
        tradingSignal = this.createFallbackTradingSignal();
      }

      // Add metadata
      tradingSignal.metadata = {
        analysis_type: "AI_VISUAL",
        timestamp: new Date().toISOString(),
        symbol: this.symbol,
        current_price: currentPrice,
        timeframes_analyzed: ["4h", "1h", "15m"],
      };

      console.log("✅ AI trading signal generated successfully");
      return tradingSignal;
    } catch (error) {
      console.error("❌ AI trading signal generation failed:", error);
      return this.createFallbackTradingSignal();
    }
  }

  /**
   * Enhanced GPT analysis with AI vision results
   * @param {Object} aiAnalysis - AI analysis results
   * @param {Object} aiTradingSignal - AI trading signal
   * @returns {Promise<string>} Enhanced GPT analysis
   */
  async analyzeWithAIVision(aiAnalysis, aiTradingSignal) {
    try {
      const { "15m": ai15m, "1h": ai1h, "4h": ai4h } = aiAnalysis;
      const currentPrice = aiTradingSignal.metadata.current_price;

      // Format AI analysis for human-readable output
      const formatTrend = (trend) => {
        const trendEmoji = { BULLISH: "🟢", BEARISH: "🔴", SIDEWAYS: "⚪" };
        return `${trendEmoji[trend.direction]} ${trend.direction}`;
      };

      const formatSignal = (action) => {
        const signalEmoji = {
          STRONG_BUY: "🟢🟢",
          BUY: "🟢",
          STRONG_SELL: "🔴🔴",
          SELL: "🔴",
          HOLD: "⚪",
          NO_TRADE: "⚪",
        };
        return `${signalEmoji[action] || "⚪"} ${action}`;
      };

      const text = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Tạo báo cáo phân tích ngắn gọn dựa trên kết quả AI vision cho mục đích học tập.

THÔNG TIN THỊ TRƯỜNG:
- Tài sản: ${this.symbol}
- Giá hiện tại: $${currentPrice}

KẾT QUẢ PHÂN TÍCH AI VISION:

KHUNG 4H: ${formatTrend(ai4h.trend)} (Tin cậy: ${
        ai4h.overall_assessment.confidence_score
      }/10)
KHUNG 1H: ${formatTrend(ai1h.trend)} (Tin cậy: ${
        ai1h.overall_assessment.confidence_score
      }/10)
KHUNG 15M: ${formatTrend(ai15m.trend)} (Tin cậy: ${
        ai15m.overall_assessment.confidence_score
      }/10)

KHUYẾN NGHỊ AI: ${formatSignal(aiTradingSignal.trading_recommendation.action)}
- Entry: ${aiTradingSignal.trading_recommendation.entry_price}
- Stop Loss: ${aiTradingSignal.trading_recommendation.stop_loss}
- Take Profit: ${aiTradingSignal.trading_recommendation.take_profit_1}
- Risk/Reward: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}

ĐÁNH GIÁ RỦI RO: ${aiTradingSignal.risk_assessment.risk_level}
- Rủi ro chính: ${aiTradingSignal.risk_assessment.key_risks.join(", ")}

YÊU CẦU PHẢN HỒI:
Tạo báo cáo phân tích ngắn gọn, mang tính giáo dục theo format:

<b>📊 PHÂN TÍCH ĐA KHUNG THỜI GIAN:</b>
• <b>4H:</b> [Xu hướng và nhận định]
• <b>1H:</b> [Xu hướng và nhận định]
• <b>15M:</b> [Xu hướng và nhận định]

<b>🎯 KHUYẾN NGHỊ AI:</b> ${aiTradingSignal.trading_recommendation.action}
• <b>Entry:</b> ${aiTradingSignal.trading_recommendation.entry_price}
• <b>Stop Loss:</b> ${aiTradingSignal.trading_recommendation.stop_loss}
• <b>Take Profit:</b> ${aiTradingSignal.trading_recommendation.take_profit_1}

<b>⚠️ QUẢN LÝ RỦI RO:</b> ${aiTradingSignal.risk_assessment.risk_level}

<b>💡 Độ tin cậy AI:</b> ${
        aiTradingSignal.overall_assessment.final_confidence
      }/10

<b>🔍 Nhận định chính:</b> ${aiTradingSignal.overall_assessment.key_message}

QUAN TRỌNG: Chỉ sử dụng thẻ HTML <b></b>. Ngắn gọn và mang tính giáo dục. Tối đa 400 ký tự. Luôn nhấn mạnh tính chất giáo dục.`;

      const response = await this.openai.chat.completions.create({
        model: this.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "Bạn là chuyên gia phân tích kỹ thuật giáo dục. Tạo báo cáo phân tích ngắn gọn, mang tính giáo dục dựa trên phân tích AI cho mục đích học tập. Chỉ sử dụng thẻ HTML <b></b>, không dùng markdown. Luôn nhấn mạnh tính chất giáo dục và không phải lời khuyên đầu tư.",
          },
          {
            role: "user",
            content: text,
          },
        ],
        max_tokens: this.defaultParams.maxTokens.analysis,
        temperature: this.defaultParams.temperature,
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error("❌ Enhanced GPT analysis failed:", error);
      return `<b>⚠️ LỖI PHÂN TÍCH AI</b>\n\nPhân tích hình ảnh AI gặp lỗi. Vui lòng kiểm tra system logs.\n\n<b>Trạng thái:</b> Cần xem xét thủ công`;
    }
  }

  /**
   * Set chart handler reference for AI visual analysis
   * @param {Object} chartHandler - Chart handler instance
   */
  setChartHandler(chartHandler) {
    this.chartHandler = chartHandler;
  }
}

export default GPTHandler;
