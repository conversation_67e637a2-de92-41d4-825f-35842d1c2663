import OpenAI from "openai";

/**
 * GPT Handler Class
 * Encapsulates all OpenAI GPT API interactions, analysis, and trading signal generation
 * Follows clean separation of concerns pattern for trading bot architecture
 */
class GPTHandler {
  constructor(apiKey, symbol, interval) {
    this.symbol = symbol;
    this.interval = interval;

    // Initialize OpenAI client
    this.openai = new OpenAI({ apiKey });

    // Model configurations
    this.models = {
      vision: "gpt-4o",
      text: "gpt-4o-mini",
      analysis: "gpt-4o-mini",
    };

    // Default parameters
    this.defaultParams = {
      temperature: 0.1,
      maxTokens: {
        vision: 2000,
        text: 800,
        analysis: 800,
      },
    };
  }

  /**
   * Analyze trading data with GPT for multi-timeframe analysis
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @param {Object} tradingSignal - Trading signal data
   * @returns {Promise<string>} GPT analysis result
   */
  async analyzeWithGPT(multiTimeframeAnalysis, tradingSignal) {
    const tf4h = multiTimeframeAnalysis["4h"];
    const tf1h = multiTimeframeAnalysis["1h"];
    const tf15m = multiTimeframeAnalysis["15m"];

    const currentPrice = tf15m.candles.at(-1).close;
    const currentRsi = tf15m.indicators.rsi.at(-1);
    const currentEma20 = tf15m.indicators.ema20.at(-1);
    const currentEma50 = tf15m.indicators.ema50.at(-1);

    const text = `You are a professional trading analyst. Analyze ${
      this.symbol
    } ${this.interval} with multi-timeframe data for educational purposes:

CURRENT MARKET DATA:
- Current Price: $${currentPrice}
- 15M Trend: ${tf15m.trend.trend} (Confidence: ${tf15m.trend.confidence.toFixed(
      2
    )})
- 1H Trend: ${tf1h.trend.trend} (Confidence: ${tf1h.trend.confidence.toFixed(
      2
    )})
- 4H Trend: ${tf4h.trend.trend} (Confidence: ${tf4h.trend.confidence.toFixed(
      2
    )})

TECHNICAL INDICATORS (15M):
- RSI(14): ${currentRsi?.toFixed(1)}
- EMA20: $${currentEma20?.toFixed(2)}
- EMA50: $${currentEma50?.toFixed(2)}

TRADING SIGNAL ANALYSIS:
- Major Trend: ${tradingSignal.majorTrend}
- Entry Signal: ${tradingSignal.entrySignal}
- Confidence: ${tradingSignal.confidence.toFixed(2)}
- Recommendation: ${tradingSignal.recommendation}

RISK LEVELS:
- Support: $${tradingSignal.riskLevels.support?.toFixed(2) || "N/A"}
- Resistance: $${tradingSignal.riskLevels.resistance?.toFixed(2) || "N/A"}
- Stop Loss: $${tradingSignal.riskLevels.stopLoss?.toFixed(2) || "N/A"}

REQUIRED FORMAT (HTML only, no markdown):

<b>📊 MULTI-TIMEFRAME ANALYSIS:</b>
• <b>4H:</b> [Trend analysis]
• <b>1H:</b> [Trend analysis] 
• <b>15M:</b> [Trend analysis]

<b>🎯 TRADING SCENARIO:</b>
• <b>Type:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Specific entry conditions]
• <b>Confirmation:</b> [Signals to wait for]
• <b>Entry/SL/TP:</b> Entry [$price], SL [$price], TP [$price] (RR 1:[ratio])
• <b>Invalidation:</b> [Conditions that cancel setup]

<b>💡 Confidence:</b> [X]%

IMPORTANT: Use only <b></b> HTML tags, no markdown. Be specific with prices. Max 500 characters.`;

    const res = await this.openai.chat.completions.create({
      model: this.models.text,
      messages: [
        {
          role: "system",
          content:
            "You are a professional trading analyst. MUST respond in EXACT HTML format requested. Use only <b></b> tags, no markdown. Be concise and professional.",
        },
        { role: "user", content: text },
      ],
    });

    return res.choices[0].message.content;
  }

  /**
   * Legacy GPT analysis for backward compatibility
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @returns {Promise<string>} Legacy GPT analysis result
   */
  async legacyAnalyzeWithGPT(candles, indicators) {
    const last = candles.at(-1);

    const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${this.symbol} khung ${
      this.interval
    } với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[
      indicators.ema20.at(-1),
      indicators.ema50.at(-1),
      indicators.ema89.at(-1),
    ]
      .map((v) => v?.toFixed(2))
      .join("/")}
- RSI(14): ${indicators.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${indicators.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]
<b>🎯 TÍN HIỆU:</b> [Mua/Bán/Chờ] - [Điều kiện entry]
<b>💰 MỤC TIÊU:</b> Entry [giá], SL [giá], TP [giá]
<b>⚠️ RỦI RO:</b> [Mức độ rủi ro và cảnh báo]
<b>📊 RSI:</b> [Phân tích RSI hiện tại]

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

    const res = await this.openai.chat.completions.create({
      model: this.models.text,
      messages: [
        {
          role: "system",
          content:
            "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp.",
        },
        { role: "user", content: text },
      ],
    });

    return res.choices[0].message.content;
  }

  /**
   * Create intelligent analysis based on market data
   * @param {string} timeframe - Timeframe identifier
   * @param {Object} timeframeData - Market data for the timeframe
   * @returns {Object} Analysis object
   */
  createIntelligentAnalysis(timeframe, timeframeData) {
    const { candles, indicators, trend } = timeframeData;
    const currentPrice = candles[candles.length - 1].close;
    const currentEma20 = indicators.ema20[indicators.ema20.length - 1];
    const currentEma50 = indicators.ema50[indicators.ema50.length - 1];
    const currentEma200 = indicators.ema200[indicators.ema200.length - 1];
    const currentRsi = indicators.rsi[indicators.rsi.length - 1];

    // Analyze price vs EMAs
    const priceVsEma20 = this.comparePriceToEma(currentPrice, currentEma20);
    const priceVsEma50 = this.comparePriceToEma(currentPrice, currentEma50);
    const priceVsEma200 = this.comparePriceToEma(currentPrice, currentEma200);

    // Analyze EMA alignment
    const emaAlignment = this.analyzeEmaAlignment(
      currentEma20,
      currentEma50,
      currentEma200
    );

    // Analyze momentum
    const momentum = this.analyzeMomentum(currentRsi, trend);

    // Generate trading signal
    const tradingSignal = this.generateTradingSignal(
      trend,
      momentum,
      currentPrice
    );

    // Calculate support/resistance levels
    const levels = this.calculateSupportResistance(candles, currentPrice);

    return {
      trend: {
        direction: trend.trend,
        strength: Math.round(trend.strength * 10),
        confidence: Math.round(trend.confidence * 10),
        reasoning: `Technical analysis for ${timeframe} - ${
          trend.trend
        } trend with ${Math.round(trend.strength * 100)}% strength`,
      },
      patterns: {
        chart_patterns: this.identifyChartPatterns(candles),
        candlestick_patterns: this.identifyCandlestickPatterns(
          candles.slice(-3)
        ),
        pattern_significance: this.getPatternSignificance(trend.confidence),
      },
      levels: {
        support: levels.support,
        resistance: levels.resistance,
        key_level_proximity: levels.proximity,
      },
      moving_averages: {
        price_vs_ema20: priceVsEma20,
        price_vs_ema50: priceVsEma50,
        price_vs_ema200: priceVsEma200,
        ema_alignment: emaAlignment,
        ema_analysis: this.generateEmaAnalysis(
          priceVsEma20,
          priceVsEma50,
          priceVsEma200,
          emaAlignment
        ),
      },
      momentum: {
        direction: momentum.direction,
        strength: momentum.strength,
        momentum_analysis: momentum.analysis,
      },
      trading_signal: {
        signal: tradingSignal.signal,
        entry_zone: tradingSignal.entry_zone,
        stop_loss: tradingSignal.stop_loss,
        take_profit: tradingSignal.take_profit,
        risk_reward: tradingSignal.risk_reward,
        signal_reasoning: tradingSignal.reasoning,
      },
      overall_assessment: {
        market_condition: this.assessMarketCondition(trend, momentum),
        trade_quality: this.assessTradeQuality(
          trend.confidence,
          momentum.strength
        ),
        confidence_score: Math.round(trend.confidence * 10),
        key_insights: `${timeframe} analysis: ${
          trend.trend
        } trend, RSI ${currentRsi.toFixed(1)}, Price ${priceVsEma20} EMA20`,
      },
    };
  }

  /**
   * Helper methods for intelligent analysis
   */

  comparePriceToEma(price, ema) {
    const threshold = 0.002; // 0.2% threshold
    if (price > ema * (1 + threshold)) return "ABOVE";
    if (price < ema * (1 - threshold)) return "BELOW";
    return "AT";
  }

  analyzeEmaAlignment(ema20, ema50, ema200) {
    if (ema20 > ema50 && ema50 > ema200) return "BULLISH";
    if (ema20 < ema50 && ema50 < ema200) return "BEARISH";
    return "MIXED";
  }

  analyzeMomentum(rsi, trend) {
    let direction = "NEUTRAL";
    let strength = 5;
    let analysis = "";

    if (rsi > 70) {
      direction = "OVERBOUGHT";
      strength = 8;
      analysis = `RSI ${rsi.toFixed(1)} indicates overbought conditions`;
    } else if (rsi < 30) {
      direction = "OVERSOLD";
      strength = 8;
      analysis = `RSI ${rsi.toFixed(1)} indicates oversold conditions`;
    } else if (rsi > 60) {
      direction = "BULLISH";
      strength = 7;
      analysis = `RSI ${rsi.toFixed(1)} shows bullish momentum`;
    } else if (rsi < 40) {
      direction = "BEARISH";
      strength = 7;
      analysis = `RSI ${rsi.toFixed(1)} shows bearish momentum`;
    } else {
      analysis = `RSI ${rsi.toFixed(1)} in neutral zone`;
    }

    return { direction, strength, analysis };
  }

  generateTradingSignal(trend, momentum, currentPrice) {
    let signal = "HOLD";
    let reasoning = "";

    // Strong trend + momentum alignment
    if (trend.trend === "BULLISH" && trend.confidence > 0.6) {
      if (
        momentum.direction === "BULLISH" ||
        momentum.direction === "OVERSOLD"
      ) {
        signal = "BUY";
        reasoning = "Strong bullish trend with supportive momentum";
      }
    } else if (trend.trend === "BEARISH" && trend.confidence > 0.6) {
      if (
        momentum.direction === "BEARISH" ||
        momentum.direction === "OVERBOUGHT"
      ) {
        signal = "SELL";
        reasoning = "Strong bearish trend with supportive momentum";
      }
    }

    if (signal === "HOLD") {
      reasoning =
        "Waiting for clearer trend confirmation or better momentum alignment";
    }

    const stopLossPercent = 2;
    const takeProfitPercent = 4;

    return {
      signal,
      entry_zone: `Around $${currentPrice.toFixed(4)}`,
      stop_loss: `$${(currentPrice * (1 - stopLossPercent / 100)).toFixed(
        4
      )} (${stopLossPercent}% rule)`,
      take_profit: `$${(currentPrice * (1 + takeProfitPercent / 100)).toFixed(
        4
      )} (${takeProfitPercent}% target)`,
      risk_reward: "1:2",
      reasoning,
    };
  }

  calculateSupportResistance(candles, currentPrice) {
    const recentCandles = candles.slice(-50); // Last 50 candles
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    // Simple support/resistance calculation
    const resistance = Math.max(...highs.slice(-20));
    const support = Math.min(...lows.slice(-20));

    let proximity = "BETWEEN_LEVELS";
    if (Math.abs(currentPrice - resistance) / currentPrice < 0.01) {
      proximity = "NEAR_RESISTANCE";
    } else if (Math.abs(currentPrice - support) / currentPrice < 0.01) {
      proximity = "NEAR_SUPPORT";
    }

    return {
      support: [support],
      resistance: [resistance],
      proximity,
    };
  }

  identifyChartPatterns(candles) {
    // Simplified pattern recognition
    const recentCandles = candles.slice(-20);
    const patterns = [];

    // Check for basic patterns
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    const recentHigh = Math.max(...highs.slice(-10));
    const recentLow = Math.min(...lows.slice(-10));
    const range = recentHigh - recentLow;

    if (range / recentLow < 0.02) {
      patterns.push("Consolidation");
    }

    return patterns;
  }

  identifyCandlestickPatterns(candles) {
    if (candles.length < 3) return [];

    const patterns = [];
    const last = candles[candles.length - 1];
    const prev = candles[candles.length - 2];

    // Simple candlestick patterns
    if (last.close > last.open && prev.close < prev.open) {
      patterns.push("Bullish Reversal");
    } else if (last.close < last.open && prev.close > prev.open) {
      patterns.push("Bearish Reversal");
    }

    return patterns;
  }

  getPatternSignificance(confidence) {
    if (confidence > 0.7) return "HIGH";
    if (confidence > 0.4) return "MEDIUM";
    return "LOW";
  }

  generateEmaAnalysis(priceVsEma20, priceVsEma50, priceVsEma200, alignment) {
    return `Price is ${priceVsEma20} EMA20, ${priceVsEma50} EMA50, ${priceVsEma200} EMA200. EMA alignment is ${alignment}.`;
  }

  assessMarketCondition(trend, momentum) {
    if (trend.confidence > 0.7) {
      return trend.trend === "BULLISH" ? "TRENDING_UP" : "TRENDING_DOWN";
    }
    return "RANGING";
  }

  assessTradeQuality(confidence, momentumStrength) {
    const score = (confidence * 10 + momentumStrength) / 2;
    if (score > 7) return "GOOD";
    if (score > 5) return "FAIR";
    return "POOR";
  }

  /**
   * Multi-timeframe analysis helper methods
   */

  analyzeTimeframeAlignment(ai4h, ai1h, ai15m) {
    const trends = [
      ai4h.trend.direction,
      ai1h.trend.direction,
      ai15m.trend.direction,
    ];
    const bullishCount = trends.filter((t) => t === "BULLISH").length;
    const bearishCount = trends.filter((t) => t === "BEARISH").length;

    let strength = "CONFLICTING";
    let reasoning = "";

    if (bullishCount === 3) {
      strength = "STRONG";
      reasoning = "All timeframes show bullish alignment";
    } else if (bearishCount === 3) {
      strength = "STRONG";
      reasoning = "All timeframes show bearish alignment";
    } else if (bullishCount >= 2) {
      strength = "MODERATE";
      reasoning = "Majority timeframes bullish";
    } else if (bearishCount >= 2) {
      strength = "MODERATE";
      reasoning = "Majority timeframes bearish";
    } else {
      strength = "WEAK";
      reasoning = "Mixed signals across timeframes";
    }

    return { strength, reasoning, trends };
  }

  determineMajorTrend(ai4h, ai1h, ai15m) {
    // Prioritize 4h trend, then 1h, then 15m
    const weights = { "4h": 0.5, "1h": 0.3, "15m": 0.2 };

    let direction = ai4h.trend.direction;
    let strength = Math.round(
      ai4h.trend.strength * weights["4h"] +
        ai1h.trend.strength * weights["1h"] +
        ai15m.trend.strength * weights["15m"]
    );

    // If 4h is sideways, look at 1h
    if (direction === "SIDEWAYS") {
      direction = ai1h.trend.direction;
      if (direction === "SIDEWAYS") {
        direction = ai15m.trend.direction;
      }
    }

    const reasoning = `Major trend determined by ${direction} bias, primarily from higher timeframes`;

    return { direction, strength, reasoning };
  }

  generateTradingRecommendation(
    majorTrend,
    timeframeAlignment,
    ai15m,
    currentPrice
  ) {
    let action = "HOLD";
    let confidence = 5;
    let positionSize = "SMALL";
    let reasoning = "Waiting for better setup";

    // Strong alignment + strong trend = stronger signal
    if (timeframeAlignment.strength === "STRONG" && majorTrend.strength >= 7) {
      if (majorTrend.direction === "BULLISH") {
        action = "BUY";
        confidence = 8;
        positionSize = "MEDIUM";
        reasoning = "Strong bullish alignment across all timeframes";
      } else if (majorTrend.direction === "BEARISH") {
        action = "SELL";
        confidence = 8;
        positionSize = "MEDIUM";
        reasoning = "Strong bearish alignment across all timeframes";
      }
    } else if (
      timeframeAlignment.strength === "MODERATE" &&
      majorTrend.strength >= 6
    ) {
      if (majorTrend.direction === "BULLISH") {
        action = "BUY";
        confidence = 6;
        reasoning = "Moderate bullish bias with decent strength";
      } else if (majorTrend.direction === "BEARISH") {
        action = "SELL";
        confidence = 6;
        reasoning = "Moderate bearish bias with decent strength";
      }
    }

    const stopLossPercent = 2;
    const tp1Percent = 3;
    const tp2Percent = 6;

    return {
      action,
      confidence,
      entry_price: `Around $${currentPrice.toFixed(4)}`,
      stop_loss: `$${(currentPrice * (1 - stopLossPercent / 100)).toFixed(4)}`,
      take_profit_1: `$${(currentPrice * (1 + tp1Percent / 100)).toFixed(4)}`,
      take_profit_2: `$${(currentPrice * (1 + tp2Percent / 100)).toFixed(4)}`,
      risk_reward_ratio: "1:3",
      position_size: positionSize,
      reasoning,
    };
  }

  assessTradingRisk(majorTrend, timeframeAlignment, ai15m) {
    let riskLevel = "MEDIUM";
    const keyRisks = [];

    if (timeframeAlignment.strength === "CONFLICTING") {
      riskLevel = "HIGH";
      keyRisks.push("Conflicting timeframe signals");
    }

    if (majorTrend.strength < 5) {
      riskLevel = "HIGH";
      keyRisks.push("Weak trend strength");
    }

    if (
      ai15m.momentum.direction === "OVERBOUGHT" ||
      ai15m.momentum.direction === "OVERSOLD"
    ) {
      keyRisks.push("Extreme momentum conditions");
    }

    if (keyRisks.length === 0) {
      riskLevel = "LOW";
      keyRisks.push("Favorable market conditions");
    }

    return {
      risk_level: riskLevel,
      key_risks: keyRisks,
      invalidation_level: "Below/above key support/resistance",
      market_conditions: `${
        majorTrend.direction
      } trend with ${timeframeAlignment.strength.toLowerCase()} alignment`,
    };
  }

  createExecutionPlan(tradingRecommendation, currentPrice) {
    const entryStrategy =
      tradingRecommendation.action === "HOLD"
        ? "Wait for clearer signals"
        : "Enter on pullback or breakout confirmation";

    const exitStrategy =
      tradingRecommendation.action === "HOLD"
        ? "N/A"
        : "Scale out at TP1 (50%), let TP2 run with trailing stop";

    const monitoringPoints = [
      `Entry: ${tradingRecommendation.entry_price}`,
      `Stop Loss: ${tradingRecommendation.stop_loss}`,
      `Take Profit 1: ${tradingRecommendation.take_profit_1}`,
    ];

    const timeHorizon =
      tradingRecommendation.action === "HOLD"
        ? "N/A"
        : "1-3 days for swing trade";

    return {
      entry_strategy: entryStrategy,
      exit_strategy: exitStrategy,
      monitoring_points: monitoringPoints,
      time_horizon: timeHorizon,
    };
  }

  createOverallAssessment(
    majorTrend,
    timeframeAlignment,
    tradingRecommendation
  ) {
    let tradeQuality = "FAIR";
    let marketOpportunity = 5;
    let finalConfidence = tradingRecommendation.confidence;

    if (timeframeAlignment.strength === "STRONG" && majorTrend.strength >= 7) {
      tradeQuality = "GOOD";
      marketOpportunity = 8;
    } else if (timeframeAlignment.strength === "CONFLICTING") {
      tradeQuality = "POOR";
      marketOpportunity = 3;
    }

    const keyMessage = `${
      majorTrend.direction
    } bias with ${timeframeAlignment.strength.toLowerCase()} timeframe alignment - ${
      tradingRecommendation.action
    } recommended`;

    return {
      trade_quality: tradeQuality,
      market_opportunity: marketOpportunity,
      final_confidence: finalConfidence,
      key_message: keyMessage,
    };
  }

  createIntelligentFallbackSignal(multiTimeframeAnalysis) {
    const currentPrice = multiTimeframeAnalysis["15m"].candles.at(-1).close;

    return {
      major_trend: {
        direction: "SIDEWAYS",
        timeframe_alignment: "WEAK",
        strength: 5,
        reasoning:
          "Fallback analysis - insufficient data for trend determination",
      },
      trading_recommendation: {
        action: "HOLD",
        confidence: 3,
        entry_price: `Around $${currentPrice.toFixed(4)}`,
        stop_loss: `$${(currentPrice * 0.98).toFixed(4)}`,
        take_profit_1: `$${(currentPrice * 1.03).toFixed(4)}`,
        take_profit_2: `$${(currentPrice * 1.06).toFixed(4)}`,
        risk_reward_ratio: "1:2",
        position_size: "SMALL",
        reasoning: "Fallback analysis - manual review required",
      },
      risk_assessment: {
        risk_level: "HIGH",
        key_risks: ["Analysis system fallback", "Manual review required"],
        invalidation_level: "N/A",
        market_conditions: "Unknown - system fallback",
      },
      execution_plan: {
        entry_strategy: "Wait for manual analysis",
        exit_strategy: "N/A",
        monitoring_points: ["Manual review required"],
        time_horizon: "N/A",
      },
      overall_assessment: {
        trade_quality: "POOR",
        market_opportunity: 2,
        final_confidence: 2,
        key_message: "System fallback - manual analysis required",
      },
      metadata: {
        analysis_type: "FALLBACK",
        timestamp: new Date().toISOString(),
        symbol: this.symbol,
        current_price: currentPrice,
        timeframes_analyzed: ["4h", "1h", "15m"],
      },
    };
  }

  /**
   * Perform intelligent analysis on multi-timeframe data
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @returns {Promise<Object>} Analysis results for all timeframes
   */
  async performAIVisualAnalysis(multiTimeframeAnalysis) {
    try {
      console.log("🔍 Starting intelligent multi-timeframe analysis...");

      const analysis = {};

      // Analyze each timeframe using market data
      for (const [timeframe, timeframeData] of Object.entries(
        multiTimeframeAnalysis
      )) {
        console.log(`📊 Analyzing ${timeframe} timeframe...`);

        analysis[timeframe] = this.createIntelligentAnalysis(
          timeframe,
          timeframeData
        );

        console.log(`✅ Successfully analyzed ${timeframe} timeframe`);
      }

      console.log("✅ Multi-timeframe analysis completed for all timeframes");
      return analysis;
    } catch (error) {
      console.error("❌ Multi-timeframe analysis failed:", error);
      throw error;
    }
  }

  /**
   * Generate intelligent trading signal based on multi-timeframe analysis
   * @param {Object} aiAnalysis - Analysis results from performAIVisualAnalysis
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @returns {Promise<Object>} Trading signal
   */
  async generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
    try {
      console.log("🎯 Generating AI-powered trading signal...");

      const { "4h": ai4h, "1h": ai1h, "15m": ai15m } = aiAnalysis;
      const currentPrice = multiTimeframeAnalysis["15m"].candles.at(-1).close;

      // Tạo prompt phân tích toàn diện cho mục đích giáo dục
      const multiTimeframePrompt = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy tạo một phân tích giáo dục dựa trên dữ liệu đa khung thời gian cho mục đích học tập và nghiên cứu.
  
  KẾT QUẢ PHÂN TÍCH AI ĐA KHUNG THỜI GIAN:
  
  PHÂN TÍCH KHUNG 4H:
  - Xu hướng: ${ai4h.trend.direction} (Sức mạnh: ${
        ai4h.trend.strength
      }/10, Độ tin cậy: ${ai4h.trend.confidence}/10)
  - Mô hình: ${ai4h.patterns.chart_patterns.join(", ") || "Không xác định được"}
  - Tín hiệu: ${ai4h.trading_signal.signal}
  - Nhận định chính: ${ai4h.overall_assessment.key_insights}
  
  PHÂN TÍCH KHUNG 1H:
  - Xu hướng: ${ai1h.trend.direction} (Sức mạnh: ${
        ai1h.trend.strength
      }/10, Độ tin cậy: ${ai1h.trend.confidence}/10)
  - Mô hình: ${ai1h.patterns.chart_patterns.join(", ") || "Không xác định được"}
  - Tín hiệu: ${ai1h.trading_signal.signal}
  - Nhận định chính: ${ai1h.overall_assessment.key_insights}
  
  PHÂN TÍCH KHUNG 15M:
  - Xu hướng: ${ai15m.trend.direction} (Sức mạnh: ${
        ai15m.trend.strength
      }/10, Độ tin cậy: ${ai15m.trend.confidence}/10)
  - Mô hình: ${
    ai15m.patterns.chart_patterns.join(", ") || "Không xác định được"
  }
  - Tín hiệu: ${ai15m.trading_signal.signal}
  - Nhận định chính: ${ai15m.overall_assessment.key_insights}
  
  DỮ LIỆU THỊ TRƯỜNG HIỆN TẠI:
  - Mã: ${this.symbol}
  - Giá hiện tại: ${currentPrice}
  - Hỗ trợ/Kháng cự 4H: ${ai4h.levels.support.join(
    ", "
  )} / ${ai4h.levels.resistance.join(", ")}
  - Hỗ trợ/Kháng cự 1H: ${ai1h.levels.support.join(
    ", "
  )} / ${ai1h.levels.resistance.join(", ")}
  - Hỗ trợ/Kháng cự 15M: ${ai15m.levels.support.join(
    ", "
  )} / ${ai15m.levels.resistance.join(", ")}
  
  KHUNG PHÂN TÍCH GIÁO DỤC:
  1. Các khung thời gian cao hơn (4H, 1H) cho thấy xu hướng tổng thể
  2. Khung 15M cung cấp chi tiết về timing và entry points
  3. Sự thống nhất giữa các khung thời gian tạo ra tín hiệu mạnh hơn
  4. Quản lý rủi ro là yếu tố quan trọng trong mọi chiến lược
  
  Đây là phân tích cho mục đích giáo dục, không phải lời khuyên đầu tư.
  
  TRẢ LỜI với phân tích giáo dục toàn diện theo định dạng JSON CHÍNH XÁC này:
  {
    "major_trend": {
      "direction": "BULLISH|BEARISH|SIDEWAYS",
      "strength": 1-10,
      "confidence": 1-10,
      "reasoning": "Analysis of 4H and 1H alignment"
    },
    "timeframe_alignment": {
      "alignment_score": 1-10,
      "alignment_type": "FULLY_ALIGNED|PARTIALLY_ALIGNED|CONFLICTING",
      "alignment_analysis": "How well do all timeframes align"
    },
    "trading_recommendation": {
      "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL|NO_TRADE",
      "confidence": 1-10,
      "entry_price": "Specific entry price or range",
      "stop_loss": "Specific stop loss level",
      "take_profit_1": "First take profit target",
      "take_profit_2": "Second take profit target",
      "risk_reward_ratio": "Risk to reward ratio",
      "position_size": "Suggested position size (SMALL|MEDIUM|LARGE)",
      "reasoning": "Detailed reasoning for this recommendation"
    },
    "risk_assessment": {
      "risk_level": "LOW|MEDIUM|HIGH",
      "key_risks": ["List of main risks"],
      "invalidation_level": "Price level that invalidates the setup",
      "market_conditions": "Current market environment assessment"
    },
    "execution_plan": {
      "entry_strategy": "How to enter the position",
      "exit_strategy": "How to manage the position",
      "monitoring_points": ["Key levels to watch"],
      "time_horizon": "Expected trade duration"
    },
    "overall_assessment": {
      "trade_quality": "EXCELLENT|GOOD|FAIR|POOR",
      "market_opportunity": 1-10,
      "final_confidence": 1-10,
      "key_message": "Most important takeaway for the trader"
    }
  }
  
  QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Xem xét tất cả khung thời gian nhưng ưu tiên xu hướng khung thời gian cao hơn cho hướng chính.`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content:
              "Bạn là chuyên gia phân tích kỹ thuật giáo dục với hơn 20 năm kinh nghiệm. Tạo phân tích giáo dục thận trọng, có lý lẽ dựa trên đa khung thời gian cho mục đích học tập. Luôn nhấn mạnh quản lý rủi ro và tính chất giáo dục của phân tích.",
          },
          {
            role: "user",
            content: multiTimeframePrompt,
          },
        ],
        max_tokens: 2000,
        temperature: 0.1,
      });

      const signalText = response.choices[0].message.content;

      let tradingSignal;
      try {
        // Clean and parse JSON response
        let jsonText = signalText.trim();

        // Look for JSON block if wrapped in markdown
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim();
        }

        // Remove any leading/trailing non-JSON text
        const jsonStart = jsonText.indexOf("{");
        const jsonEnd = jsonText.lastIndexOf("}");

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
        }

        console.log("🔍 Attempting to parse AI trading signal...");
        tradingSignal = JSON.parse(jsonText);

        // Validate required fields
        if (
          !tradingSignal.major_trend ||
          !tradingSignal.trading_recommendation
        ) {
          throw new Error("Missing required fields in trading signal");
        }
      } catch (parseError) {
        console.error("❌ Failed to parse AI trading signal:", parseError);
        console.error(`Raw response: ${signalText.substring(0, 200)}...`);
        tradingSignal = createFallbackTradingSignal();
      }

      // Add metadata
      tradingSignal.metadata = {
        analysis_type: "AI_VISUAL",
        timestamp: new Date().toISOString(),
        symbol: this.symbol,
        current_price: currentPrice,
        timeframes_analyzed: ["4h", "1h", "15m"],
      };

      console.log("✅ AI trading signal generated successfully");
      return tradingSignal;
    } catch (error) {
      console.error("❌ AI trading signal generation failed:", error);
      return createFallbackTradingSignal();
    }
  }

  async analyzeWithAIVision(aiAnalysis, aiTradingSignal) {
    try {
      const { "15m": ai15m, "1h": ai1h, "4h": ai4h } = aiAnalysis;
      const currentPrice = aiTradingSignal.metadata.current_price;

      // Format AI analysis for human-readable output
      const formatTrend = (trend) => {
        const trendEmoji = { BULLISH: "🟢", BEARISH: "🔴", SIDEWAYS: "⚪" };
        return `${trendEmoji[trend.direction]} ${trend.direction}`;
      };

      const formatSignal = (action) => {
        const signalEmoji = {
          STRONG_BUY: "🟢🟢",
          BUY: "🟢",
          STRONG_SELL: "🔴🔴",
          SELL: "🔴",
          HOLD: "⚪",
          NO_TRADE: "⚪",
        };
        return `${signalEmoji[action] || "⚪"} ${action}`;
      };

      const text = `Bạn là chuyên gia phân tích kỹ thuật giáo dục tạo báo cáo học tập toàn diện dựa trên phân tích biểu đồ AI cho mục đích giáo dục.
  
  KẾT QUẢ PHÂN TÍCH HÌNH ẢNH AI:

  GIÁ HIỆN TẠI: ${currentPrice}
  
  XU HƯỚNG ĐA KHUNG THỜI GIAN:
  - 4H: ${formatTrend(ai4h.trend)} (Độ tin cậy: ${ai4h.trend.confidence}/10)
  - 1H: ${formatTrend(ai1h.trend)} (Độ tin cậy: ${ai1h.trend.confidence}/10)
  - 15M: ${formatTrend(ai15m.trend)} (Độ tin cậy: ${ai15m.trend.confidence}/10)
  
  MÔ HÌNH BIỂU ĐỒ ĐƯỢC XÁC ĐỊNH:
  - Mô hình 4H: ${ai4h.patterns.chart_patterns.join(", ") || "Không có"}
  - Mô hình 1H: ${ai1h.patterns.chart_patterns.join(", ") || "Không có"}
  - Mô hình 15M: ${ai15m.patterns.chart_patterns.join(", ") || "Không có"}
  
  KHUYẾN NGHỊ GIAO DỊCH AI:
  - Hành động: ${formatSignal(aiTradingSignal.trading_recommendation.action)}
  - Độ tin cậy: ${aiTradingSignal.trading_recommendation.confidence}/10
  - Vào lệnh: ${aiTradingSignal.trading_recommendation.entry_price}
  - Cắt lỗ: ${aiTradingSignal.trading_recommendation.stop_loss}
  - Chốt lời: ${aiTradingSignal.trading_recommendation.take_profit_1}
  - Tỷ lệ R/R: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}
  
  ĐIỀU KIỆN THỊ TRƯỜNG:
  - Xu hướng chính: ${formatTrend(aiTradingSignal.major_trend)} (Sức mạnh: ${
        aiTradingSignal.major_trend.strength
      }/10)
  - Sự thống nhất: ${aiTradingSignal.timeframe_alignment.alignment_type}
  - Chất lượng giao dịch: ${aiTradingSignal.overall_assessment.trade_quality}
  
  Tạo báo cáo phân tích giáo dục theo định dạng HTML CHÍNH XÁC này (chỉ cho mục đích học tập):
  
  <b>🤖 PHÂN TÍCH HÌNH ẢNH AI:</b>
  • <b>Xu hướng chính:</b> [${
    aiTradingSignal.major_trend.direction
  }] - [Lý do ngắn gọn]
  • <b>Đồng bộ khung thời gian:</b> [Trạng thái và chất lượng thống nhất]
  • <b>Mô hình chính:</b> [Các mô hình quan trọng nhất được tìm thấy]
  
  <b>🎯 TÍN HIỆU GIAO DỊCH AI:</b>
  • <b>Hành động:</b> [${aiTradingSignal.trading_recommendation.action}]
  • <b>Vùng vào lệnh:</b> [Giá/khoảng vào lệnh]
  • <b>Quản lý rủi ro:</b> SL ${
    aiTradingSignal.trading_recommendation.stop_loss
  } | TP ${aiTradingSignal.trading_recommendation.take_profit_1}
  • <b>Tỷ lệ R/R:</b> ${
    aiTradingSignal.trading_recommendation.risk_reward_ratio
  }
  
  <b>💡 Độ tin cậy AI:</b> ${
    aiTradingSignal.overall_assessment.final_confidence
  }/10
  
  <b>🔍 Nhận định chính:</b> ${aiTradingSignal.overall_assessment.key_message}
  
  QUAN TRỌNG: Chỉ sử dụng thẻ HTML <b></b>. Ngắn gọn và mang tính giáo dục. Tối đa 400 ký tự. Luôn nhấn mạnh tính chất giáo dục.`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content:
              "Bạn là chuyên gia phân tích kỹ thuật giáo dục. Tạo báo cáo phân tích ngắn gọn, mang tính giáo dục dựa trên phân tích AI cho mục đích học tập. Chỉ sử dụng thẻ HTML <b></b>, không dùng markdown. Luôn nhấn mạnh tính chất giáo dục và không phải lời khuyên đầu tư.",
          },
          {
            role: "user",
            content: text,
          },
        ],
        max_tokens: 800,
        temperature: 0.1,
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error("❌ Enhanced GPT analysis failed:", error);
      return `<b>⚠️ LỖI PHÂN TÍCH AI</b>\n\nPhân tích hình ảnh AI gặp lỗi. Vui lòng kiểm tra system logs.\n\n<b>Trạng thái:</b> Cần xem xét thủ công`;
    }
  }

  /**
   * Set chart handler reference for AI visual analysis
   * @param {Object} chartHandler - Chart handler instance
   */
  setChartHandler(chartHandler) {
    this.chartHandler = chartHandler;
  }
}

export default GPTHandler;
