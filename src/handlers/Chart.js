import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { createCanvas, loadImage } from "canvas";
import fs from "fs";
import dayjs from "dayjs";

// Chart.js + plugins
import {
  Chart,
  TimeScale,
  LinearScale,
  BarController,
  BarElement,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";

/**
 * Chart Handler Class
 * Encapsulates all chart data processing, generation, and management logic
 * Follows clean separation of concerns pattern for trading bot architecture
 */
class ChartHandler {
  constructor(symbol, interval) {
    this.symbol = symbol;
    this.interval = interval;

    // Chart configuration constants
    this.CHART_COLORS = {
      upColor: "#26a69a",
      downColor: "#ef5350",
      upBorderColor: "#26a69a",
      downBorderColor: "#ef5350",
      background: "#141416",
      whiteBackground: "#FFFFFF",
      textColor: "#AAA",
      gridColor: "rgba(255,255,255,0.08)",
      gridColorLight: "rgba(255,255,255,0.05)",
    };

    // Default chart dimensions
    this.DEFAULT_DIMENSIONS = {
      price: { width: 1200, height: 560 },
      rsi: { width: 1200, height: 180 },
      volume: { width: 1200, height: 160 },
      ai: { width: 1200, height: 800 },
    };

    // Initialize chart plugins and components
    this.initializeChartComponents();
  }

  /**
   * Initialize Chart.js components and custom plugins
   */
  initializeChartComponents() {
    // Custom Candlestick Plugin
    this.candlestickPlugin = {
      id: "candlestick",
      afterDatasetsDraw: this.drawCandlesticks.bind(this),
    };

    // Chart registration function
    this.registerAll = (Chart) => {
      Chart.register(
        TimeScale,
        LinearScale,
        BarController,
        BarElement,
        LineController,
        LineElement,
        PointElement,
        Tooltip,
        Legend,
        Title,
        this.candlestickPlugin
      );
    };
  }

  /**
   * Custom candlestick drawing logic
   * @param {Object} chart - Chart.js chart instance
   */
  drawCandlesticks(chart) {
    const { ctx, data, scales } = chart;
    const dataset = data.datasets.find(
      (ds) => ds.label && ds.label.includes(this.symbol)
    );
    if (!dataset || !dataset.data[0] || dataset.data[0].o === undefined) return;

    const xScale = scales.x;
    const yScale = scales.y;

    dataset.data.forEach((candle, index) => {
      if (!candle || candle.o === undefined) return;

      const x = xScale.getPixelForValue(candle.x);
      const yOpen = yScale.getPixelForValue(candle.o);
      const yHigh = yScale.getPixelForValue(candle.h);
      const yLow = yScale.getPixelForValue(candle.l);
      const yClose = yScale.getPixelForValue(candle.c);

      const isUp = candle.c >= candle.o;
      const candleWidth = Math.max(
        2,
        (xScale.width / dataset.data.length) * 0.6
      );

      const upColor = this.CHART_COLORS.upColor;
      const downColor = this.CHART_COLORS.downColor;
      const upBorderColor = this.CHART_COLORS.upBorderColor;
      const downBorderColor = this.CHART_COLORS.downBorderColor;

      ctx.save();

      // Draw the wick (high-low line)
      ctx.strokeStyle = isUp ? upColor : downColor;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, yHigh);
      ctx.lineTo(x, yLow);
      ctx.stroke();

      // Draw the body (open-close rectangle)
      const bodyTop = Math.min(yOpen, yClose);
      const bodyHeight = Math.max(1, Math.abs(yClose - yOpen));

      if (isUp) {
        // Bullish candle - filled with up color
        ctx.fillStyle = upColor;
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = upBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      } else {
        // Bearish candle - filled with down color
        ctx.fillStyle = downColor;
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = downBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      }

      ctx.restore();
    });
  }

  /**
   * Format time labels for chart axes
   * @param {Array} candles - Array of candle data
   * @param {number} value - Value to format
   * @param {number} index - Index in the array
   * @returns {string} Formatted time string
   */
  formatTimeLabel(candles, value, index) {
    if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
      return dayjs(candles[value].time).format("MM/DD HH:mm");
    }
    return "";
  }

  /**
   * Create base chart configuration
   * @param {string} type - Chart type
   * @param {Object} datasets - Chart datasets
   * @param {Object} options - Chart options
   * @returns {Object} Chart configuration
   */
  createBaseChartConfig(type, datasets, options = {}) {
    return {
      type,
      data: { datasets },
      options: {
        responsive: false,
        ...options,
      },
    };
  }

  /**
   * Create standard chart scales configuration
   * @param {Array} candles - Candle data for time formatting
   * @param {Object} yOptions - Y-axis specific options
   * @returns {Object} Scales configuration
   */
  createStandardScales(candles, yOptions = {}) {
    return {
      x: {
        type: "linear",
        ticks: {
          color: this.CHART_COLORS.textColor,
          maxTicksLimit: 12,
          callback: (value, index) =>
            this.formatTimeLabel(candles, value, index),
        },
        grid: { color: this.CHART_COLORS.gridColor },
      },
      y: {
        position: "left",
        ticks: { color: this.CHART_COLORS.textColor },
        grid: { color: this.CHART_COLORS.gridColor },
        ...yOptions,
      },
    };
  }

  /**
   * Create tooltip configuration for candlestick charts
   * @param {Array} candles - Candle data
   * @returns {Object} Tooltip configuration
   */
  createCandlestickTooltip(candles) {
    return {
      enabled: true,
      callbacks: {
        title: function (context) {
          const index = context[0].dataIndex;
          return dayjs(candles[index].time).format("YYYY-MM-DD HH:mm");
        },
        label: function (context) {
          const index = context.dataIndex;
          const candleData = context.chart.data.datasets[0].data;
          const candle = candleData[index];
          if (candle && candle.o !== undefined) {
            return [
              `Open: ${candle.o.toFixed(4)}`,
              `High: ${candle.h.toFixed(4)}`,
              `Low: ${candle.l.toFixed(4)}`,
              `Close: ${candle.c.toFixed(4)}`,
            ];
          }
          return context.dataset.label + ": " + context.parsed.y;
        },
      },
    };
  }

  /**
   * Render price panel with candlesticks and indicators
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @param {Object} multiTimeframeData - Multi-timeframe data for trend indicators
   * @returns {Buffer} Chart image buffer
   */
  async renderPricePanel(
    candles,
    indicators,
    width = this.DEFAULT_DIMENSIONS.price.width,
    height = this.DEFAULT_DIMENSIONS.price.height,
    multiTimeframeData = null
  ) {
    const canvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour: this.CHART_COLORS.background,
      chartCallback: this.registerAll,
    });

    const candleData = candles.map((c, i) => ({
      x: i,
      o: c.open,
      h: c.high,
      l: c.low,
      c: c.close,
      time: c.time,
    }));

    const datasets = [
      {
        type: "line",
        label: `${this.symbol} ${this.interval}`,
        data: candleData,
        showLine: false,
        pointRadius: 0,
        borderWidth: 0,
        backgroundColor: "transparent",
      },
      // Sonic R PAC indicators
      {
        type: "line",
        label: "PAC High (EMA34)",
        data: candles.map((_, i) => ({ x: i, y: indicators.pacH[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 1,
        borderColor: "#ff9800",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "PAC Low (EMA34)",
        data: candles.map((_, i) => ({ x: i, y: indicators.pacL[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 1,
        borderColor: "#ff5722",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "PAC Close (EMA34)",
        data: candles.map((_, i) => ({ x: i, y: indicators.pacC[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 2,
        borderColor: "#9c27b0",
        backgroundColor: "transparent",
      },
      // EMA overlays
      {
        type: "line",
        label: "EMA20",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema20[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 2,
        borderColor: "#2196f3",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA50",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema50[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 2,
        borderColor: "#4caf50",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA89",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema89[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 2,
        borderColor: "#ffeb3b",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA200",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema200[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 2,
        borderColor: "#f44336",
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA610",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema610[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 1,
        borderColor: "#795548",
        backgroundColor: "transparent",
      },
      // Multi-timeframe trend indicators (if available)
      ...(multiTimeframeData
        ? this.createMultiTimeframeTrendIndicators(candles, multiTimeframeData)
        : []),
    ];

    const config = this.createBaseChartConfig("line", datasets, {
      plugins: {
        title: {
          display: true,
          text: `${this.symbol} ${this.interval.toUpperCase()} Binance`,
          color: "#FFFFFF",
          font: { size: 18, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#DDD" },
        },
        tooltip: this.createCandlestickTooltip(candles),
      },
      scales: this.createStandardScales(candles),
    });

    return canvas.renderToBuffer(config, "image/png");
  }

  /**
   * Render RSI panel with RSI indicators
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @returns {Buffer} Chart image buffer
   */
  async renderRsiPanel(
    candles,
    indicators,
    width = this.DEFAULT_DIMENSIONS.rsi.width,
    height = this.DEFAULT_DIMENSIONS.rsi.height
  ) {
    const canvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour: this.CHART_COLORS.background,
      chartCallback: this.registerAll,
    });

    const datasets = [
      {
        label: "RSI Upper 80",
        data: candles.map((_, i) => ({ x: i, y: 80 })),
        borderWidth: 1,
        pointRadius: 0,
        borderColor: "#666",
        backgroundColor: "transparent",
      },
      {
        label: "RSI Lower 20",
        data: candles.map((_, i) => ({ x: i, y: 20 })),
        borderWidth: 1,
        pointRadius: 0,
        borderColor: "#666",
        backgroundColor: "transparent",
      },
      {
        label: "RSI(14)",
        data: candles.map((_, i) => ({ x: i, y: indicators.rsi[i] })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: "#2196f3",
        backgroundColor: "transparent",
      },
      {
        label: "EMA9(RSI)",
        data: candles.map((_, i) => ({ x: i, y: indicators.rsiEma9[i] })),
        borderWidth: 1,
        pointRadius: 0,
        borderColor: "#ff9800",
        backgroundColor: "transparent",
      },
      {
        label: "WMA45(RSI)",
        data: candles.map((_, i) => ({ x: i, y: indicators.rsiWma45[i] })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: "#9c27b0",
        backgroundColor: "transparent",
      },
    ];

    const config = this.createBaseChartConfig("line", datasets, {
      plugins: {
        legend: { display: true, labels: { color: "#DDD" } },
        title: { display: true, text: "RSI Panel", color: "#FFF" },
      },
      scales: this.createStandardScales(candles, { min: 0, max: 100 }),
    });

    return canvas.renderToBuffer(config, "image/png");
  }

  /**
   * Render volume panel with volume bars
   * @param {Array} candles - Candle data
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @returns {Buffer} Chart image buffer
   */
  async renderVolumePanel(
    candles,
    width = this.DEFAULT_DIMENSIONS.volume.width,
    height = this.DEFAULT_DIMENSIONS.volume.height
  ) {
    const canvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour: this.CHART_COLORS.background,
      chartCallback: this.registerAll,
    });

    const volumeData = candles.map((c, i) => {
      const bgColor =
        i === 0
          ? "rgba(38,166,154,0.6)"
          : c.close >= candles[i - 1].close
          ? "rgba(38,166,154,0.6)"
          : "rgba(239,83,80,0.6)";
      return { x: i, y: c.volume, backgroundColor: bgColor };
    });

    const config = this.createBaseChartConfig(
      "bar",
      [
        {
          label: "Volume",
          data: volumeData,
          borderWidth: 0,
          backgroundColor: volumeData.map((d) => d.backgroundColor),
        },
      ],
      {
        plugins: {
          legend: { display: false },
          title: { display: true, text: "Volume", color: "#FFF" },
        },
        scales: {
          x: {
            type: "linear",
            ticks: {
              color: this.CHART_COLORS.textColor,
              maxTicksLimit: 12,
              callback: (value, index) =>
                this.formatTimeLabel(candles, value, index),
            },
            grid: { color: this.CHART_COLORS.gridColorLight },
          },
          y: {
            ticks: { color: this.CHART_COLORS.textColor },
            grid: { color: this.CHART_COLORS.gridColorLight },
          },
        },
      }
    );

    return canvas.renderToBuffer(config, "image/png");
  }

  /**
   * Compose multiple chart panels into a single image
   * @param {Buffer} priceBuf - Price panel buffer
   * @param {Buffer} rsiBuf - RSI panel buffer
   * @param {Buffer} volBuf - Volume panel buffer
   * @param {string} outPath - Output file path
   * @returns {string} Output file path
   */
  async composePanels(priceBuf, rsiBuf, volBuf, outPath = "chart.png") {
    const priceImg = await loadImage(priceBuf);
    const rsiImg = await loadImage(rsiBuf);
    const volImg = await loadImage(volBuf);

    const gap = 6;
    const width = Math.max(priceImg.width, rsiImg.width, volImg.width);
    const height = priceImg.height + rsiImg.height + volImg.height + gap * 2;

    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext("2d");

    // Background
    ctx.fillStyle = "#0f1012";
    ctx.fillRect(0, 0, width, height);

    let y = 0;
    ctx.drawImage(priceImg, 0, y);
    y += priceImg.height + gap;
    ctx.drawImage(rsiImg, 0, y);
    y += rsiImg.height + gap;
    ctx.drawImage(volImg, 0, y);

    const buffer = canvas.toBuffer("image/png");
    fs.writeFileSync(outPath, buffer);
    return outPath;
  }

  /**
   * Generate complete chart set (price, RSI, volume) and compose them
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @param {Object} multiTimeframeData - Multi-timeframe data
   * @param {string} outputPath - Output file path
   * @returns {string} Output file path
   */
  async generateCompleteChart(
    candles,
    indicators,
    multiTimeframeData = null,
    outputPath = "chart.png"
  ) {
    const [priceBuf, rsiBuf, volBuf] = await Promise.all([
      this.renderPricePanel(
        candles,
        indicators,
        undefined,
        undefined,
        multiTimeframeData
      ),
      this.renderRsiPanel(candles, indicators),
      this.renderVolumePanel(candles),
    ]);

    return this.composePanels(priceBuf, rsiBuf, volBuf, outputPath);
  }

  /**
   * Create multi-timeframe trend indicators for chart overlay
   * @param {Array} candles - Candle data
   * @param {Object} multiTimeframeData - Multi-timeframe analysis data
   * @returns {Array} Array of indicator datasets
   */
  createMultiTimeframeTrendIndicators(candles, multiTimeframeData) {
    const indicators = [];

    // Add trend direction indicators at the top of the chart
    const highestPrice = Math.max(...candles.map((c) => c.high));
    const lowestPrice = Math.min(...candles.map((c) => c.low));
    const priceRange = highestPrice - lowestPrice;

    // Position trend indicators at different levels
    const trendLevels = {
      "4h": highestPrice + priceRange * 0.05,
      "1h": highestPrice + priceRange * 0.03,
      "15m": highestPrice + priceRange * 0.01,
    };

    // Create trend indicator lines for each timeframe
    Object.entries(multiTimeframeData).forEach(([timeframe, data]) => {
      const trend = data.trend.trend;
      const confidence = data.trend.confidence;

      // Color based on trend
      let color = "#666666"; // Default gray
      if (trend === "BULLISH") color = `rgba(76, 175, 80, ${confidence})`;
      else if (trend === "BEARISH") color = `rgba(244, 67, 54, ${confidence})`;
      else color = `rgba(255, 193, 7, ${confidence})`;

      // Add horizontal line showing trend
      indicators.push({
        type: "line",
        label: `${timeframe.toUpperCase()} Trend: ${trend}`,
        data: candles.map((_, i) => ({ x: i, y: trendLevels[timeframe] })),
        borderWidth: 3,
        pointRadius: 0,
        borderColor: color,
        backgroundColor: "transparent",
        borderDash: trend === "SIDEWAYS" ? [5, 5] : [],
      });
    });

    // Add support and resistance levels if available
    if (multiTimeframeData["15m"] && multiTimeframeData["15m"].riskLevels) {
      const riskLevels = multiTimeframeData["15m"].riskLevels;

      // Support level
      if (riskLevels.support) {
        indicators.push({
          type: "line",
          label: "Support",
          data: candles.map((_, i) => ({ x: i, y: riskLevels.support })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: "rgba(76, 175, 80, 0.7)",
          backgroundColor: "transparent",
          borderDash: [10, 5],
        });
      }

      // Resistance level
      if (riskLevels.resistance) {
        indicators.push({
          type: "line",
          label: "Resistance",
          data: candles.map((_, i) => ({ x: i, y: riskLevels.resistance })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: "rgba(244, 67, 54, 0.7)",
          backgroundColor: "transparent",
          borderDash: [10, 5],
        });
      }
    }

    return indicators;
  }

  /**
   * Generate clean chart for AI analysis with white background and clear indicators
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @param {string} timeframe - Timeframe label
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @returns {Buffer} Chart image buffer
   */
  async generateCleanChartForAI(
    candles,
    indicators,
    timeframe,
    width = this.DEFAULT_DIMENSIONS.ai.width,
    height = this.DEFAULT_DIMENSIONS.ai.height
  ) {
    const canvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour: this.CHART_COLORS.whiteBackground, // White background for better AI analysis
      chartCallback: this.registerAll,
    });

    const candleData = candles.map((c, i) => ({
      x: i,
      o: c.open,
      h: c.high,
      l: c.low,
      c: c.close,
      time: c.time,
    }));

    const datasets = [
      // Candlestick data (will be drawn by plugin)
      {
        type: "line",
        label: `${this.symbol} ${timeframe}`,
        data: candleData,
        showLine: false,
        pointRadius: 0,
        borderWidth: 0,
        backgroundColor: "transparent",
      },
      // Key EMAs for trend analysis
      {
        type: "line",
        label: "EMA20",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema20[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 3,
        borderColor: "#2196F3", // Blue
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA50",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema50[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 3,
        borderColor: "#FF9800", // Orange
        backgroundColor: "transparent",
      },
      {
        type: "line",
        label: "EMA200",
        data: candles.map((_, i) => ({ x: i, y: indicators.ema200[i] })),
        spanGaps: true,
        pointRadius: 0,
        borderWidth: 3,
        borderColor: "#F44336", // Red
        backgroundColor: "transparent",
      },
    ];

    const config = this.createBaseChartConfig("line", datasets, {
      plugins: {
        title: {
          display: true,
          text: `${this.symbol} ${timeframe.toUpperCase()} - AI Analysis`,
          color: "#000000",
          font: { size: 16, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#000000", font: { size: 12, weight: "bold" } },
        },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" },
            maxTicksLimit: 10,
            callback: (value, index) =>
              this.formatTimeLabel(candles, value, index),
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1,
          },
        },
        y: {
          position: "right",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" },
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1,
          },
        },
      },
    });

    return canvas.renderToBuffer(config, "image/png");
  }

  /**
   * Generate AI-optimized charts for all timeframes
   * @param {Object} multiTimeframeAnalysis - Multi-timeframe analysis data
   * @returns {Object} Chart data for each timeframe
   */
  async generateMultiTimeframeChartsForAI(multiTimeframeAnalysis) {
    console.log("📊 Generating AI-optimized charts for visual analysis...");

    const charts = {};
    const chartPromises = [];

    // Generate clean charts for each timeframe
    for (const [timeframe, data] of Object.entries(multiTimeframeAnalysis)) {
      const promise = this.generateCleanChartForAI(
        data.candles,
        data.indicators,
        timeframe,
        1200,
        800
      ).then((buffer) => {
        const filename = `ai-chart-${timeframe}.png`;
        fs.writeFileSync(filename, buffer);
        charts[timeframe] = {
          buffer,
          filename,
          path: `./${filename}`,
        };
        console.log(`✅ Generated ${timeframe} chart: ${filename}`);
      });

      chartPromises.push(promise);
    }

    await Promise.all(chartPromises);
    return charts;
  }
}

export default ChartHandler;
